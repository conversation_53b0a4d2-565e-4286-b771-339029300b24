import { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { FaMusic, FaSpinner } from 'react-icons/fa';
import { toast } from 'sonner';

const MAX_SAFE_PLAYLIST_SIZE = 100;
const TRACKS_PER_PAGE = 20;

interface SpotifyImage {
  url: string;
  height: number | null;
  width: number | null;
}

interface SpotifyArtist {
  id: string;
  name: string;
  uri: string;
}

interface SpotifyAlbum {
  id: string;
  name: string;
  images: SpotifyImage[];
}

interface SpotifyTrack {
  id: string;
  name: string;
  uri: string;
  duration_ms: number;
  artists: SpotifyArtist[];
  album: SpotifyAlbum;
}

interface RandomizedPlaylist {
  id: string;
  name: string;
  description?: string;
  images?: SpotifyImage[];
  tracks: SpotifyTrack[];
  uri: string;
  snapshot_id: string;
}

const getProgressMessage = (trackCount: number, progress: number) => {
  const percentage = Math.round(progress);
  const processedTracks = Math.round((trackCount * progress) / 100);
  const baseMessage = `Processing ${percentage}% (${processedTracks} of ${trackCount} tracks)`;
  
  if (trackCount <= 50) {
    return `${baseMessage} 🎲`;
  }

  let stageMessage = "";
  if (progress < 33) {
    stageMessage = trackCount > 500 
      ? "\n⏳ Large playlist - this might take a while..."
      : "\n🚀 Shuffle engine warming up...";
  } else if (progress < 66) {
    stageMessage = trackCount > 500
      ? "\n📱 Still working on it..."
      : "\n🎵 Making progress!";
  } else if (progress < 95) {
    stageMessage = trackCount > 500
      ? "\n🌟 Final stretch!"
      : "\n🎸 Almost there!";
  }
  
  return `${baseMessage}${stageMessage}`;
};

export default function Dashboard() {
  const [randomizedPlaylist, setRandomizedPlaylist] = useState<RandomizedPlaylist | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [isShuffling, setIsShuffling] = useState(false);
  const [shuffleProgress, setShuffleProgress] = useState(0);
  const [shuffleError, setShuffleError] = useState<string | null>(null);
  const [trackCount, setTrackCount] = useState(0);

  // Listen for shuffle events from the Sidebar
  useEffect(() => {
    const handleShuffleEvent = (event: MessageEvent) => {
      if (!event.data.type || !event.data.type.startsWith('SHUFFLE_')) return;
      
      console.log('Processing shuffle event:', event.data);

      if (event.data.type === 'SHUFFLE_START') {
        const trackCount = event.data.trackCount || 0;
        
        if (trackCount > MAX_SAFE_PLAYLIST_SIZE) {
          toast('Large Playlist Detected', {
            description: `This playlist has ${trackCount} tracks. Shuffling might take a while.`,
            action: {
              label: 'Understood',
              onClick: () => console.log('User acknowledged large playlist warning')
            },
            duration: 5000,
          });
        }
        
        setIsShuffling(true);
        setShuffleError(null);
        setShuffleProgress(0);
        setTrackCount(trackCount);

        // Show loading toast
        toast.loading('Starting shuffle...', {
          id: 'shuffle-progress',
        });

      } else if (event.data.type === 'SHUFFLE_PROGRESS') {
        setIsShuffling(true);
        setShuffleError(null);
        setShuffleProgress(event.data.progress);
        
        // Update loading toast with progress
        toast.loading(`Shuffling: ${Math.round(event.data.progress)}%`, {
          id: 'shuffle-progress',
        });

        if (event.data.playlist) {
          setRandomizedPlaylist(prev => ({
            ...prev,
            ...event.data.playlist,
            tracks: prev?.tracks || []
          }));
        }
      } else if (event.data.type === 'SHUFFLE_COMPLETE') {
        setIsShuffling(false);
        setShuffleError(null);
        setRandomizedPlaylist({
          ...event.data.playlist,
          tracks: event.data.playlist.tracks || []
        });

        // Dismiss loading toast and show success
        toast.dismiss('shuffle-progress');
        toast.success('Shuffle Complete!', {
          description: `Successfully shuffled ${trackCount} tracks`,
          duration: 3000,
        });
      } else if (event.data.type === 'SHUFFLE_ERROR') {
        setIsShuffling(false);
        const errorMessage = event.data.error || "An unknown error occurred";
        setShuffleError(errorMessage);
        setRandomizedPlaylist(null);
        
        // Dismiss loading toast and show error
        toast.dismiss('shuffle-progress');
        toast.error('Shuffle Failed', {
          description: errorMessage,
          action: {
            label: 'Try Again',
            onClick: () => window.location.reload()
          },
          duration: 5000,
        });
      }
    };

    window.addEventListener('message', handleShuffleEvent);
    return () => window.removeEventListener('message', handleShuffleEvent);
  }, [trackCount]);

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Safely get paginated tracks
  const paginatedTracks = randomizedPlaylist?.tracks 
    ? randomizedPlaylist.tracks.slice(
        (currentPage - 1) * TRACKS_PER_PAGE,
        currentPage * TRACKS_PER_PAGE
      )
    : [];

  const totalPages = randomizedPlaylist?.tracks 
    ? Math.ceil(randomizedPlaylist.tracks.length / TRACKS_PER_PAGE)
    : 0;

  return (
    <div className="w-full h-full bg-background">
      {/* Header is now handled by Layout */}

      {/* Main Grid Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-min">
        {/* Shuffle Status Card */}
        {isShuffling && (
          <Card className="col-span-full p-4 sm:p-6">
            <div className="flex flex-col space-y-4">
              <div className="flex items-center space-x-2">
                <h3 className="text-base sm:text-lg font-semibold">
                  {trackCount > 500 
                    ? "🚨 Large Playlist Shuffle" 
                    : trackCount > 50 
                      ? "🎵 Shuffling Collection" 
                      : "🎲 Quick Shuffle"}
                </h3>
                <FaSpinner className="animate-spin" />
              </div>
              <p className="text-sm whitespace-pre-line text-muted-foreground">
                {getProgressMessage(trackCount, shuffleProgress)}
              </p>
              {trackCount > MAX_SAFE_PLAYLIST_SIZE && (
                <p className="text-xs text-yellow-600 dark:text-yellow-400">
                  ⚠️ Large playlists might take longer or encounter issues
                </p>
              )}
            </div>
          </Card>
        )}

        {/* Error Card */}
        {shuffleError && (
          <Card className="col-span-full p-4 border-red-500 bg-red-50 dark:bg-red-900/10">
            <div className="flex flex-col space-y-2">
              <p className="text-red-600 dark:text-red-400">{shuffleError}</p>
              <p className="text-sm text-red-500 dark:text-red-300">
                Try again with a smaller playlist or contact support if the issue persists.
              </p>
            </div>
          </Card>
        )}

        {/* Playlist Display */}
        {randomizedPlaylist && (
          <div className="col-span-full">
            <Card className="p-4">
              <div className="flex flex-col space-y-4">
                {/* Playlist Header */}
                <div className="flex items-center space-x-4">
                  {randomizedPlaylist.images?.[0]?.url ? (
                    <img 
                      src={randomizedPlaylist.images[0].url}
                      alt={randomizedPlaylist.name}
                      className="w-16 h-16 rounded-lg"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-accent rounded-lg flex items-center justify-center">
                      <FaMusic className="w-8 h-8" />
                    </div>
                  )}
                  <div>
                    <h2 className="text-lg font-semibold">{randomizedPlaylist.name}</h2>
                    <p className="text-sm text-muted-foreground">
                      {randomizedPlaylist.tracks?.length || 0} tracks
                    </p>
                  </div>
                </div>

                {/* Tracks Grid */}
                <div className="grid gap-2">
                  {paginatedTracks.map((track, index) => (
                    <div 
                      key={track.id}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-accent/50 transition-colors"
                    >
                      <span className="w-8 text-center text-muted-foreground">
                        {(currentPage - 1) * TRACKS_PER_PAGE + index + 1}
                      </span>
                      {track.album.images?.[0] ? (
                        <img 
                          src={track.album.images[0].url}
                          alt={track.album.name}
                          className="w-10 h-10 rounded"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-accent rounded flex items-center justify-center">
                          <FaMusic className="w-5 h-5" />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{track.name}</p>
                        <p className="text-xs text-muted-foreground truncate">
                          {track.artists.map(a => a.name).join(', ')}
                        </p>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {formatDuration(track.duration_ms)}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center items-center space-x-2 mt-4">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 rounded-md bg-accent hover:bg-accent/80 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <span className="text-sm text-muted-foreground">
                      Page {currentPage} of {totalPages}
                    </span>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 rounded-md bg-accent hover:bg-accent/80 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                )}
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
