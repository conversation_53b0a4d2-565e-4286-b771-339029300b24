import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080'

interface SpotifyImage {
  url: string
  height: number | null
  width: number | null
}

interface UserProfile {
  id: string
  display_name: string
  images?: SpotifyImage[]
  email?: string
  country?: string
  product?: string
  type: string
  uri: string
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const token = ref<string | null>(null)
  const refreshTokenStr = ref<string | null>(null)
  const profile = ref<UserProfile | null>(null)
  const isLoading = ref(true)

  // Computed
  const isAuthenticated = computed(() => !!token.value && !!profile.value)

  // Actions
  const fetchProfile = async (accessToken: string): Promise<boolean> => {
    try {
      console.log('Fetching profile with token:', accessToken.substring(0, 10) + '...')
      const response = await axios.get<UserProfile>(`${BACKEND_URL}/api/me`, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
      
      if (response.data) {
        console.log('Profile fetched successfully:', response.data)
        profile.value = response.data
        return true
      }
      return false
    } catch (error) {
      console.error('Error fetching profile:', error)
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        // Try to refresh token
        const newToken = await refreshAccessToken()
        if (newToken) {
          // Retry with new token
          return fetchProfile(newToken)
        }
      }
      throw new Error('Failed to fetch profile')
    }
  }

  const refreshAccessToken = async (): Promise<string | null> => {
    if (!refreshTokenStr.value) {
      console.error('No refresh token available')
      return null
    }

    try {
      console.log('Refreshing access token...')
      const response = await axios.get(`${BACKEND_URL}/auth/refresh`, {
        params: { refresh_token: refreshTokenStr.value }
      })

      if (response.data?.access_token) {
        const newToken = response.data.access_token
        sessionStorage.setItem('spotify_token', newToken)
        token.value = newToken
        console.log('Token refreshed successfully')
        return newToken
      }
      return null
    } catch (error) {
      console.error('Error refreshing token:', error)
      // Clear invalid tokens
      logout()
      return null
    }
  }

  const processTokens = async (newToken: string, newRefreshToken: string): Promise<void> => {
    console.log('Processing new tokens...')
    isLoading.value = true
    
    try {
      // Store tokens first
      sessionStorage.setItem('spotify_token', newToken)
      sessionStorage.setItem('spotify_refresh_token', newRefreshToken)
      token.value = newToken
      refreshTokenStr.value = newRefreshToken
      
      // Then try to fetch profile to validate token
      console.log('Fetching profile to validate token...')
      await fetchProfile(newToken)
      
      console.log('Tokens processed and validated successfully')
    } catch (error) {
      console.error('Failed to process tokens:', error)
      // Clean up on error
      sessionStorage.removeItem('spotify_token')
      sessionStorage.removeItem('spotify_refresh_token')
      token.value = null
      refreshTokenStr.value = null
      profile.value = null
      throw error // Re-throw to be handled by caller
    } finally {
      isLoading.value = false
    }
  }

  const initialize = async (): Promise<void> => {
    try {
      isLoading.value = true
      
      // Check session storage for tokens
      const storedToken = sessionStorage.getItem('spotify_token')
      const storedRefreshToken = sessionStorage.getItem('spotify_refresh_token')
      
      if (storedToken && storedRefreshToken) {
        console.log('Found stored tokens, initializing...')
        token.value = storedToken
        refreshTokenStr.value = storedRefreshToken
        
        try {
          await fetchProfile(storedToken)
        } catch (error) {
          console.error('Failed to fetch profile with stored token:', error)
          sessionStorage.removeItem('spotify_token')
          sessionStorage.removeItem('spotify_refresh_token')
          token.value = null
          refreshTokenStr.value = null
          profile.value = null
        }
      } else {
        console.log('No stored tokens found')
        token.value = null
        refreshTokenStr.value = null
        profile.value = null
      }
    } catch (error) {
      console.error('Error in auth initialization:', error)
      token.value = null
      refreshTokenStr.value = null
      profile.value = null
    } finally {
      isLoading.value = false
    }
  }

  const logout = (): void => {
    console.log('Logging out...')
    sessionStorage.removeItem('spotify_token')
    sessionStorage.removeItem('spotify_refresh_token')
    token.value = null
    refreshTokenStr.value = null
    profile.value = null
  }

  return {
    // State
    token,
    refreshTokenStr,
    profile,
    isLoading,
    // Computed
    isAuthenticated,
    // Actions
    fetchProfile,
    refreshAccessToken,
    processTokens,
    initialize,
    logout
  }
})
