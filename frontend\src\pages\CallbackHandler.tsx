import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { authUtils } from '../utils/auth';
import { toast } from 'sonner';

export default function CallbackHandler() {
  const navigate = useNavigate();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Log the full URL and search params immediately
        const fullUrl = window.location.href;
        const searchParams = window.location.search;
        const hashParams = window.location.hash;
        
        console.log('=== Callback Debug Info ===');
        console.log('Full URL:', fullUrl);
        console.log('Search Params:', searchParams);
        console.log('Hash Params:', hashParams);
        
        // Check if we're actually at the callback URL
        if (!window.location.pathname.includes('/callback')) {
          console.error('Not at callback URL:', window.location.pathname);
          toast.error('Invalid callback URL');
          navigate('/');
          return;
        }

        const params = new URLSearchParams(searchParams);
        const hashSearchParams = new URLSearchParams(hashParams.replace('#', '?'));
        
        // Log all available parameters
        console.log('URL Parameters:', Object.fromEntries(params.entries()));
        console.log('Hash Parameters:', Object.fromEntries(hashSearchParams.entries()));
        
        // Try to get code from both search params and hash
        const code = params.get('code') || hashSearchParams.get('code');
        const error = params.get('error') || hashSearchParams.get('error');
        const state = params.get('state') || hashSearchParams.get('state');

        console.log('Extracted Parameters:', {
          code: code ? `${code.slice(0, 10)}...` : null,
          error,
          state
        });

        if (error) {
          console.error('Spotify auth error:', error);
          toast.error(`Authentication failed: ${error}`);
          navigate('/');
          return;
        }

        if (!code) {
          console.error('No code found in URL or hash params');
          console.error('URL:', fullUrl);
          toast.error('No authorization code received');
          navigate('/');
          return;
        }

        console.log('Attempting to exchange code for tokens...');
        const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/auth/token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({ code })
        });

        console.log('Token exchange response status:', response.status);
        const responseText = await response.text();
        console.log('Raw response:', responseText);

        if (!response.ok) {
          let errorMessage = 'Failed to exchange code for tokens';
          try {
            const errorData = JSON.parse(responseText);
            errorMessage = errorData.detail || errorMessage;
          } catch (e) {
            console.error('Failed to parse error response:', e);
          }
          throw new Error(errorMessage);
        }

        const data = JSON.parse(responseText);
        console.log('Successfully received token response');

        if (!data.access_token || !data.refresh_token) {
          throw new Error('Invalid token response: missing tokens');
        }

        // Store tokens in session storage
        authUtils.storeTokens(data.access_token, data.refresh_token, 3600);
        
        // Verify authentication
        console.log('Verifying authentication...');
        const isAuth = await authUtils.isAuthenticated();
        
        if (!isAuth) {
          console.error('Failed to verify authentication');
          toast.error('Failed to verify authentication');
          navigate('/');
          return;
        }

        // Success!
        console.log('Authentication successful!');
        toast.success('Successfully logged in');
        navigate('/layout');
      } catch (error) {
        console.error('Callback error:', error);
        toast.error(error instanceof Error ? error.message : 'Authentication failed');
        navigate('/');
      }
    };

    // Run the callback handler
    handleCallback();
  }, [navigate]);

  return (
    <div className="h-screen flex items-center justify-center">
      <div className="flex flex-col items-center gap-4">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full" />
        <div className="text-sm text-muted-foreground">Processing authentication...</div>
      </div>
    </div>
  );
}