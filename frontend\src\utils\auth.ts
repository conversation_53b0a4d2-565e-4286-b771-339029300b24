interface TokenInfo {
  accessToken: string;
  refreshToken: string;
  expiresAt?: number;
}

export const AUTH_STORAGE_KEY = 'spotify_auth';

export const authUtils = {
  // Store tokens with expiry
  storeTokens(accessToken: string, refreshToken: string, expiresIn: number = 3600) {
    const expiresAt = Date.now() + expiresIn * 1000;
    const tokenInfo: TokenInfo = {
      accessToken,
      refreshToken,
      expiresAt,
    };
    sessionStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(tokenInfo));
  },

  // Get stored tokens
  getTokens(): TokenInfo | null {
    try {
      const stored = sessionStorage.getItem(AUTH_STORAGE_KEY);
      if (!stored) return null;
      return JSON.parse(stored);
    } catch (error) {
      console.error('Error parsing stored tokens:', error);
      return null;
    }
  },

  // Check if token is expired
  isTokenExpired(): boolean {
    const tokens = this.getTokens();
    if (!tokens?.expiresAt) return true;
    // Add a 60-second buffer to prevent edge cases
    return Date.now() >= (tokens.expiresAt - 60000);
  },

  // Clear tokens (logout)
  clearTokens() {
    sessionStorage.removeItem(AUTH_STORAGE_KEY);
  },

  // Get valid access token (refreshing if needed)
  async getValidToken(): Promise<string | null> {
    const tokens = this.getTokens();
    if (!tokens) return null;

    if (this.isTokenExpired()) {
      try {
        const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/auth/refresh?refresh_token=${tokens.refreshToken}`);

        if (!response.ok) {
          this.clearTokens();
          return null;
        }

        const data = await response.json();
        this.storeTokens(data.access_token, tokens.refreshToken, 3600);
        return data.access_token;
      } catch (error) {
        console.error('Error refreshing token:', error);
        this.clearTokens();
        return null;
      }
    }

    return tokens.accessToken;
  },

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      const token = await this.getValidToken();
      if (!token) return false;

      // Verify token with backend
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/me`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      return response.ok;
    } catch {
      return false;
    }
  }
};