version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8001:8000"
    volumes:
      - ./backend:/app
    environment:
      - SPOTIFY_CLIENT_ID=${SPOTIFY_CLIENT_ID}
      - SPOTIFY_CLIENT_SECRET=${SPOTIFY_CLIENT_SECRET}
      - FRONTEND_URL=http://localhost:5173
      - BACKEND_URL=http://localhost:8000
      - SPOTIFY_REDIRECT_URI=http://localhost:5173/callback
      - SPOTIFY_SCOPES=user-library-read playlist-read-private playlist-modify-public playlist-modify-private user-top-read playlist-read-collaborative
    networks:
      - spotify-network

networks:
  spotify-network:
    driver: bridge 