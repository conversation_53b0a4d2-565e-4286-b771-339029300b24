<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-secondary/20">
    <div class="w-full max-w-md p-8">
      <div class="bg-card rounded-lg shadow-lg p-8 border">
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold text-foreground mb-2"><PERSON><PERSON></h1>
          <p class="text-muted-foreground">True Playlist Randomization</p>
        </div>

        <div v-if="isLoading" class="flex justify-center">
          <div class="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>

        <div v-else class="space-y-4">
          <button
            @click="handleLogin"
            :disabled="isLoading"
            class="w-full bg-green-500 hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
            </svg>
            Connect with Spotify
          </button>

          <p class="text-xs text-muted-foreground text-center">
            We'll redirect you to Spotify to authorize access to your playlists.
            No personal data is stored on our servers.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()
const isLoading = ref(false)

// Watch for authentication changes
watch(() => authStore.isAuthenticated, (isAuth) => {
  if (isAuth && !authStore.isLoading) {
    router.push('/layout')
  }
})

// Redirect if already authenticated
onMounted(() => {
  if (authStore.isAuthenticated && !authStore.isLoading) {
    router.push('/layout')
  }
})

const handleLogin = async () => {
  try {
    isLoading.value = true
    
    const response = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080'}/auth/login`)
    const data = await response.json()
    
    if (data.auth_url) {
      // Redirect to Spotify authorization
      window.location.href = data.auth_url
    } else {
      throw new Error('No authorization URL received')
    }
  } catch (error) {
    console.error('Login failed:', error)
    // You might want to show a toast notification here
  } finally {
    isLoading.value = false
  }
}
</script>
