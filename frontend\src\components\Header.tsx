import { Button } from "../components/ui/button";
import { Sun, Moon } from "lucide-react";
import { useTheme } from "../components/theme-provider";
import { useAuth } from '../contexts/AuthContext';

const Header = () => {
  const { theme, setTheme } = useTheme();
  const { profile, logout } = useAuth();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
      <div className="container flex h-14 items-center justify-between px-4 sm:px-6">
        <div className="font-bold">Spotify App</div>
        <div className="flex items-center gap-4">
          {profile && (
            <div className="flex items-center gap-3">
              {profile.images?.[0]?.url && (
                <img
                  src={profile.images[0].url}
                  alt={profile.display_name}
                  className="h-8 w-8 rounded-full"
                />
              )}
              <span className="text-sm font-medium">{profile.display_name}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={logout}
              >
                Logout
              </Button>
            </div>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "light" ? "dark" : "light")}
          >
            {theme === "light" ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;
