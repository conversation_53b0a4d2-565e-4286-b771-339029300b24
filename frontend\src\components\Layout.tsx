import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';
import { useAuth } from '../contexts/AuthContext';
import { Loader2 } from 'lucide-react';
import Dashboard from '../pages/Dashboard';
import { useEffect } from 'react';

const Layout = () => {
  const { isAuthenticated, profile, token, isLoading } = useAuth();
  const location = useLocation();

  // Debug auth state
  useEffect(() => {
    console.log('Layout mounted');
    console.log('Auth state:', { 
      isAuthenticated, 
      hasProfile: !!profile, 
      hasToken: !!token,
      isLoading,
      path: location.pathname 
    });
  }, [isAuthenticated, profile, token, isLoading, location]);

  // Show loading state while auth is being checked
  if (isLoading) {
    console.log('Auth loading...');
    return (
      <div className="h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    console.log('Not authenticated, redirecting to login');
    const returnTo = encodeURIComponent(location.pathname + location.search);
    return <Navigate to={`/?returnTo=${returnTo}`} replace />;
  }

  return (
    <div className="flex flex-col h-screen bg-background">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <main className="flex-1 overflow-auto bg-background p-4 sm:p-6 pl-[88px] md:pl-60">
          <Routes>
            <Route index element={<Dashboard />} />
            <Route path="dashboard" element={<Dashboard />} />
            {/* Add other routes here */}
            <Route path="*" element={<Navigate to="/layout" replace />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

export default Layout; 