import { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import { cn } from '../lib/utils';
import { Button } from './ui/button';
import { FaChevronLeft, FaChevronRight, FaHome, FaSearch, FaHeart } from 'react-icons/fa';
import { SidebarLink } from './SidebarLink';

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;

interface Playlist {
  id: string;
  name: string;
  type: 'private' | 'public' | 'subscribed';
  images: { url: string }[];
  owner: {
    display_name: string;
  };
}

interface CategorizedPlaylists {
  private: Playlist[];
  public: Playlist[];
  subscribed: Playlist[];
}

const categorizePlaylists = (playlists: Playlist[]): CategorizedPlaylists => {
  const assignedPlaylists = new Set<string>();

  return playlists.reduce((acc: CategorizedPlaylists, playlist) => {
    if (assignedPlaylists.has(playlist.id)) {
      return acc;
    }

    let primaryType: 'private' | 'public' | 'subscribed';
    
    if (playlist.type === 'private') {
      primaryType = 'private';
    } else if (playlist.type === 'subscribed') {
      primaryType = 'subscribed';
    } else {
      primaryType = 'public';
    }

    assignedPlaylists.add(playlist.id);
    acc[primaryType].push(playlist);

    return acc;
  }, {
    private: [],
    public: [],
    subscribed: []
  });
};

const Sidebar = () => {
  const { token } = useAuth();
  const [playlists, setPlaylists] = useState<CategorizedPlaylists>({
    private: [],
    public: [],
    subscribed: []
  });
  const [error, setError] = useState<string | null>(null);
  const [collapsed, setCollapsed] = useState(false);

  // Handle responsive collapse
  useEffect(() => {
    const handleResize = () => {
      setCollapsed(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initialize on mount

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Simplified playlist fetching
  useEffect(() => {
    const fetchPlaylists = async () => {
      try {
        const response = await axios.get(`${BACKEND_URL}/api/playlists`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        if (!response.data) {
          setError('No playlists found');
          return;
        }

        const categorizedPlaylists = categorizePlaylists(response.data);
        setPlaylists(categorizedPlaylists);
        setError(null);
      } catch (error) {
        console.error('Error fetching playlists:', error);
        setError('Failed to load playlists. Please try again.');
      }
    };

    fetchPlaylists();
  }, [token]);

  return (
    <aside
      className={cn(
        "fixed left-0 top-[72px] h-[calc(100vh-72px)] bg-background border-r transition-all duration-300 z-30",
        collapsed ? "w-[88px]" : "w-60",
        "p-4 flex-shrink-0"
      )}
    >
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-end mb-4">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full border border-border bg-background hover:bg-accent hover:text-accent-foreground"
            onClick={() => setCollapsed(!collapsed)}
          >
            {collapsed ? (
              <FaChevronRight className="h-5 w-5" />
            ) : (
              <FaChevronLeft className="h-5 w-5" />
            )}
          </Button>
        </div>

        <div className="space-y-4 overflow-y-auto flex-1">
          <div className="space-y-1">
            <SidebarLink to="/layout/dashboard" tooltip="Home" collapsed={collapsed}>
              <FaHome className="h-5 w-5" />
              {!collapsed && "Home"}
            </SidebarLink>
            <SidebarLink to="/search" tooltip="Search" collapsed={collapsed}>
              <FaSearch className="h-5 w-5" />
              {!collapsed && "Search"}
            </SidebarLink>
            <SidebarLink to="/liked" tooltip="Liked Songs" collapsed={collapsed}>
              <FaHeart className="h-5 w-5" />
              {!collapsed && "Liked Songs"}
            </SidebarLink>
          </div>

          {error && (
            <div className="text-sm text-destructive px-4">
              {error}
            </div>
          )}

          {Object.entries(playlists).map(([category, items]) => (
            items.length > 0 && (
              <div key={category} className="space-y-1">
                <h2 className={cn(
                  "text-sm font-semibold text-muted-foreground uppercase tracking-wider",
                  collapsed && "sr-only"
                )}>
                  {category}
                </h2>
                {items.map((playlist: Playlist) => (
                  <SidebarLink
                    key={playlist.id}
                    to={`/playlist/${playlist.id}`}
                    tooltip={playlist.name}
                    collapsed={collapsed}
                  >
                    {playlist.images[0] ? (
                      <img
                        src={playlist.images[0].url}
                        alt={playlist.name}
                        className="h-5 w-5 rounded"
                      />
                    ) : (
                      <div className="h-5 w-5 rounded bg-accent" />
                    )}
                    {!collapsed && playlist.name}
                  </SidebarLink>
                ))}
              </div>
            )
          ))}
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
