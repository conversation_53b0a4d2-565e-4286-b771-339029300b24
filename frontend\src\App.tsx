import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from './components/theme-provider';
import { Toaster } from 'sonner';
import './styles/globals.css';
import { Suspense, lazy } from 'react';
import { useAuth } from './hooks/useAuth';
import CallbackHandler from './pages/CallbackHandler';
import { ErrorBoundary } from './components/ErrorBoundary';

// Lazy loaded components
const Login = lazy(() => import('./pages/Login'));
const Layout = lazy(() => import('./components/Layout'));

// Loading component
const LoadingSpinner = () => (
  <div className="h-screen flex items-center justify-center">
    <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
  </div>
);

// Protected Route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

// Routes component
const AppRoutes = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route 
        path="/" 
        element={
          isAuthenticated ? 
            <Navigate to="/layout" replace /> : 
            <Suspense fallback={<LoadingSpinner />}>
              <Login />
            </Suspense>
        } 
      />
      
      <Route
        path="/callback"
        element={
          <ErrorBoundary>
            <CallbackHandler />
          </ErrorBoundary>
        }
      />

      {/* Protected routes */}
      <Route
        path="/layout/*"
        element={
          <ProtectedRoute>
            <Suspense fallback={<LoadingSpinner />}>
              <Layout />
            </Suspense>
          </ProtectedRoute>
        }
      />

      {/* Catch-all route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

function App() {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="spotify-theme">
      <Router>
        <Suspense fallback={<LoadingSpinner />}>
          <AppRoutes />
          <Toaster 
            position="top-right"
            expand={true}
            richColors
            theme="dark"
          />
        </Suspense>
      </Router>
    </ThemeProvider>
  );
}

export default App;
