import { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;

interface SpotifyImage {
  url: string;
  height: number | null;
  width: number | null;
}

interface UserProfile {
  id: string;
  display_name: string;
  images?: SpotifyImage[];
  email?: string;
  country?: string;
  product?: string;
  type: string;
  uri: string;
}

export interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  token: string | null;
  profile: UserProfile | null;
  processTokens: (token: string, refreshToken: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<string | null>;
}

export const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [token, setToken] = useState<string | null>(null);
  const [refreshTokenStr, setRefreshTokenStr] = useState<string | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const refreshAccessToken = async (): Promise<string | null> => {
    try {
      if (!refreshTokenStr) return null;

      const response = await axios.get(`${BACKEND_URL}/auth/refresh`, {
        headers: { Authorization: `Bearer ${refreshTokenStr}` }
      });

      if (response.data?.access_token) {
        const newToken = response.data.access_token;
        sessionStorage.setItem('spotify_token', newToken);
        setToken(newToken);
        return newToken;
      }
      return null;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return null;
    }
  };

  const fetchProfile = async (accessToken: string) => {
    try {
      console.log('Fetching profile with token:', accessToken.substring(0, 10) + '...');
      const response = await axios.get<UserProfile>(`${BACKEND_URL}/api/me`, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });
      
      if (response.data) {
        console.log('Profile fetched successfully:', response.data);
        setProfile(response.data);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error fetching profile:', error);
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        // Try to refresh token
        const newToken = await refreshAccessToken();
        if (newToken) {
          // Retry with new token
          return fetchProfile(newToken);
        }
      }
      throw new Error('Failed to fetch profile');
    }
  };

  const processTokens = async (newToken: string, newRefreshToken: string) => {
    console.log('Processing new tokens...');
    setIsLoading(true);
    
    try {
      // Store tokens first
      sessionStorage.setItem('spotify_token', newToken);
      sessionStorage.setItem('spotify_refresh_token', newRefreshToken);
      setToken(newToken);
      setRefreshTokenStr(newRefreshToken);
      
      // Then try to fetch profile to validate token
      console.log('Fetching profile to validate token...');
      await fetchProfile(newToken);
      
      // Double check that everything is set correctly
      const finalToken = sessionStorage.getItem('spotify_token');
      console.log('Final auth state:', {
        hasToken: !!finalToken,
        hasProfile: !!profile,
        isAuthenticated: !!finalToken && !!profile
      });
      
      console.log('Tokens processed and validated successfully');
    } catch (error) {
      console.error('Failed to process tokens:', error);
      // Clean up on error
      sessionStorage.removeItem('spotify_token');
      sessionStorage.removeItem('spotify_refresh_token');
      setToken(null);
      setRefreshTokenStr(null);
      setProfile(null);
      throw error; // Re-throw to be handled by caller
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize auth state
  useEffect(() => {
    let mounted = true;
    
    const initialize = async () => {
      try {
        setIsLoading(true);
        
        // Check session storage for tokens
        const storedToken = sessionStorage.getItem('spotify_token');
        const storedRefreshToken = sessionStorage.getItem('spotify_refresh_token');
        
        if (storedToken && storedRefreshToken && mounted) {
          console.log('Found stored tokens, initializing...');
          setToken(storedToken);
          setRefreshTokenStr(storedRefreshToken);
          
          try {
            await fetchProfile(storedToken);
          } catch (error) {
            console.error('Failed to fetch profile with stored token:', error);
            sessionStorage.removeItem('spotify_token');
            sessionStorage.removeItem('spotify_refresh_token');
            if (mounted) {
              setToken(null);
              setRefreshTokenStr(null);
              setProfile(null);
            }
          }
        } else {
          console.log('No stored tokens found');
          if (mounted) {
            setToken(null);
            setRefreshTokenStr(null);
            setProfile(null);
          }
        }
      } catch (error) {
        console.error('Error in auth initialization:', error);
        if (mounted) {
          setToken(null);
          setRefreshTokenStr(null);
          setProfile(null);
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    initialize();

    return () => {
      mounted = false;
    };
  }, []);

  const logout = () => {
    console.log('Logging out...');
    sessionStorage.removeItem('spotify_token');
    sessionStorage.removeItem('spotify_refresh_token');
    setToken(null);
    setRefreshTokenStr(null);
    setProfile(null);
    navigate('/', { replace: true });
  };

  const value = {
    token,
    isAuthenticated: !!token && !!profile,
    profile,
    isLoading,
    logout,
    refreshToken: refreshAccessToken,
    processTokens
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 