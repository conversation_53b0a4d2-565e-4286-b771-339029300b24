import type { RouteRecordRaw } from 'vue-router'

// Lazy load components
const Login = () => import('../views/Login.vue')
const Layout = () => import('../components/Layout.vue')
const Dashboard = () => import('../views/Dashboard.vue')
const CallbackHandler = () => import('../views/CallbackHandler.vue')

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/callback',
    name: 'Callback',
    component: CallbackHandler,
    meta: { requiresAuth: false }
  },
  {
    path: '/layout',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: Dashboard
      },
      {
        path: 'dashboard',
        name: 'DashboardExplicit',
        component: Dashboard
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]
