import { Button } from "../components/ui/button"
import { Card, CardContent } from "../components/ui/card"
import { FaSpotify } from 'react-icons/fa';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

const Login = () => {
  const { isAuthenticated, isLoading, login } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      navigate('/layout', { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate]);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="h-screen flex items-center justify-center">
      <Card className="w-[400px]">
        <CardContent className="pt-6 px-8">
          <h1 className="text-3xl font-bold text-center mb-6">Welcome to Spotify App</h1>
          <Button 
            className="w-full"
            size="lg"
            onClick={login}
          >
            <FaSpotify className="mr-2 h-4 w-4" />
            Login with Spotify
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default Login;
