import { Link } from 'react-router-dom';
import { cn } from '../lib/utils';
import {
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TooltipRoot,
} from './ui/tooltip';

interface SidebarLinkProps {
  to: string;
  children: React.ReactNode;
  tooltip?: string;
  collapsed?: boolean;
}

export const SidebarLink = ({ to, children, tooltip, collapsed }: SidebarLinkProps) => {
  const content = (
    <Link
      to={to}
      className={cn(
        "flex items-center gap-2 p-2 rounded-md",
        "text-muted-foreground hover:text-foreground",
        "hover:bg-accent transition-colors",
        collapsed && "justify-center"
      )}
    >
      {children}
    </Link>
  );

  if (collapsed && tooltip) {
    return (
      <TooltipProvider>
        <TooltipRoot>
          <TooltipTrigger asChild>
            {content}
          </TooltipTrigger>
          <TooltipContent side="right" sideOffset={10}>
            {tooltip}
          </TooltipContent>
        </TooltipRoot>
      </TooltipProvider>
    );
  }

  return content;
}; 