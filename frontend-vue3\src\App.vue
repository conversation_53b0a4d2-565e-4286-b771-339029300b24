<script setup lang="ts">
import { onMounted, Suspense } from 'vue'
import { RouterView } from 'vue-router'
import { useAuthStore } from './stores/auth'
import LoadingSpinner from './components/LoadingSpinner.vue'

const authStore = useAuthStore()

onMounted(() => {
  authStore.initialize()
})
</script>

<template>
  <div class="min-h-screen bg-background text-foreground">
    <Suspense>
      <template #default>
        <RouterView />
      </template>
      <template #fallback>
        <LoadingSpinner />
      </template>
    </Suspense>
  </div>
</template>

<style>
/* Global styles are handled by Tailwind CSS */
</style>
