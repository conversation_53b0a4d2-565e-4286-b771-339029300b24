<template>
  <div class="h-screen flex items-center justify-center">
    <div class="flex flex-col items-center gap-4">
      <div class="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full" />
      <div class="text-sm text-muted-foreground">Processing authentication...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

onMounted(async () => {
  await handleCallback()
})

const handleCallback = async () => {
  try {
    // Log the full URL and search params immediately
    const fullUrl = window.location.href
    const searchParams = window.location.search
    const hashParams = window.location.hash
    
    console.log('=== Callback Debug Info ===')
    console.log('Full URL:', fullUrl)
    console.log('Search Params:', searchParams)
    console.log('Hash Params:', hashParams)
    
    // Check if we're actually at the callback URL
    if (!window.location.pathname.includes('/callback')) {
      console.error('Not at callback URL:', window.location.pathname)
      router.push('/')
      return
    }

    const params = new URLSearchParams(searchParams)
    const hashSearchParams = new URLSearchParams(hashParams.replace('#', '?'))
    
    // Log all available parameters
    console.log('URL Parameters:', Object.fromEntries(params.entries()))
    console.log('Hash Parameters:', Object.fromEntries(hashSearchParams.entries()))
    
    // Try to get code from both search params and hash
    const code = params.get('code') || hashSearchParams.get('code')
    const error = params.get('error') || hashSearchParams.get('error')
    const state = params.get('state') || hashSearchParams.get('state')

    console.log('Extracted Parameters:', {
      code: code ? `${code.slice(0, 10)}...` : null,
      error,
      state
    })

    if (error) {
      console.error('Spotify auth error:', error)
      router.push('/')
      return
    }

    if (!code) {
      console.error('No code found in URL or hash params')
      console.error('URL:', fullUrl)
      router.push('/')
      return
    }

    console.log('Attempting to exchange code for tokens...')
    const response = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080'}/auth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({ code })
    })

    console.log('Token exchange response status:', response.status)
    const responseText = await response.text()
    console.log('Raw response:', responseText)

    if (!response.ok) {
      let errorMessage = 'Failed to exchange code for tokens'
      try {
        const errorData = JSON.parse(responseText)
        errorMessage = errorData.detail || errorMessage
      } catch (e) {
        console.error('Failed to parse error response:', e)
      }
      throw new Error(errorMessage)
    }

    const data = JSON.parse(responseText)
    console.log('Successfully received token response')

    if (!data.access_token || !data.refresh_token) {
      throw new Error('Invalid token response: missing tokens')
    }

    // Process tokens through the auth store
    await authStore.processTokens(data.access_token, data.refresh_token)
    
    // Success!
    console.log('Authentication successful!')
    router.push('/layout')
  } catch (error) {
    console.error('Callback error:', error)
    router.push('/')
  }
}
</script>
