import { useEffect, useState } from 'react';
import { authUtils } from '../utils/auth';
import { toast } from 'sonner';

export function useAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const isAuth = await authUtils.isAuthenticated();
      setIsAuthenticated(isAuth);
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async () => {
    try {
      console.log('Starting login process...');
      setIsLoading(true);
      
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/auth/login`);
      console.log('Login response status:', response.status);
      
      const data = await response.json();
      console.log('Login response:', { auth_url: data.auth_url?.slice(0, 50) + '...' });
      
      if (!data.auth_url) {
        throw new Error('No auth URL received');
      }

      console.log('Redirecting to Spotify auth URL...');
      window.location.href = data.auth_url;
    } catch (error) {
      console.error('Login failed:', error);
      toast.error('Failed to start login process');
      setIsLoading(false);
    }
  };

  const logout = () => {
    console.log('Logging out...');
    authUtils.clearTokens();
    setIsAuthenticated(false);
    window.location.href = '/';
  };

  return { 
    isAuthenticated, 
    isLoading, 
    login, 
    logout,
    checkAuth 
  };
}
 