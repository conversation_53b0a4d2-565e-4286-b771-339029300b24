import './assets/main.css'

import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import App from './App.vue'

// Import routes
import { routes } from './router/index'

// Create router
const router = createRouter({
  history: createWebHistory(),
  routes,
})

// Create pinia store
const pinia = createPinia()

// Create app
const app = createApp(App)
app.use(pinia)
app.use(router)

// Router guards
router.beforeEach(async (to, from, next) => {
  const { useAuthStore } = await import('./stores/auth')
  const authStore = useAuthStore()

  // Wait for auth initialization if still loading
  if (authStore.isLoading) {
    // Wait a bit for initialization to complete
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  if (requiresAuth && !authStore.isAuthenticated) {
    // Redirect to login with return URL
    const returnTo = encodeURIComponent(to.fullPath)
    next(`/?returnTo=${returnTo}`)
  } else if (!requiresAuth && authStore.isAuthenticated && to.path === '/') {
    // Redirect authenticated users away from login
    const returnTo = to.query.returnTo as string
    next(returnTo ? decodeURIComponent(returnTo) : '/layout')
  } else {
    next()
  }
})

app.mount('#app')
