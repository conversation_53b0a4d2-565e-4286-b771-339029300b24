<template>
  <div class="flex flex-col h-screen bg-background">
    <Header />
    <div class="flex flex-1 overflow-hidden">
      <Sidebar />
      <main class="flex-1 overflow-auto bg-background p-4 sm:p-6 pl-[88px] md:pl-60">
        <RouterView />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import Header from './Header.vue'
import Sidebar from './Sidebar.vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Watch for authentication changes
watch(() => authStore.isAuthenticated, (isAuth) => {
  if (!isAuth && !authStore.isLoading) {
    const returnTo = encodeURIComponent(route.path + (route.query ? '?' + new URLSearchParams(route.query as Record<string, string>).toString() : ''))
    router.push(`/?returnTo=${returnTo}`)
  }
})

// Check authentication on mount
onMounted(() => {
  if (!authStore.isAuthenticated && !authStore.isLoading) {
    const returnTo = encodeURIComponent(route.path + (route.query ? '?' + new URLSearchParams(route.query as Record<string, string>).toString() : ''))
    router.push(`/?returnTo=${returnTo}`)
  }
})
</script>
